
**版本: 1.0 (最终版)**
**日期: 2025年8月22日**

---

### **1. 引言 (Introduction)**

#### **1.1 目的 (Purpose)**
本产品的目标是为影视爱好者提供一个轻量、高效、专注且无干扰的个人影视剧集追踪和管理工具。它旨在解决用户在追踪多部剧集时进度混乱、容易遗忘新集更新的问题，通过简洁的界面和精准的追踪功能，优化用户的观影管理体验，为用户的娱乐生活带来秩序感和掌控感。

#### **1.2 范围 (Scope)**
本项目旨在开发一款名为 **IMList** 的个人影视剧集追踪和管理工具。

**产品核心功能包括：**

*   **一、 核心追踪与管理:** 个人化影视库、状态管理（想看/在看/已看）、精细化至单集的进度追踪、跨设备数据同步。
*   **二、 发现与信息:** 强大的影视搜索与发现功能、详尽的影视信息展示、整合“在哪看”的流媒体平台链接。
*   **三、 规划与提醒:** 及时的新集播出提醒、可视化的播出日历。
*   **四、 个性化与统计:** 灵活的自定义列表、可视化的个人观看统计报告。
*   **五、 商业化:** 采用广告与高级订阅（Freemium）的混合商业模式。

**范围排除项 (Out of Scope):**
*   用户社交功能（如好友、动态分享）。
*   用户原创内容功能（UGC，如评论、论坛）。
*   在线视频播放功能。
*   复杂的、基于机器学习的个性化推荐算法。

#### **1.3 定义、首字母缩写和缩略语**
*   **SRS:** Software Requirement Specification (软件需求规格说明书)
*   **TMDB:** The Movie Database
*   **IMList:** 本产品名称
*   **API:** Application Programming Interface (应用程序编程接口)
*   **SDK:** Software Development Kit (软件开发工具包)
*   **UGC:** User-Generated Content (用户原创内容)
*   **ANR:** Application Not Responding (应用无响应)

---

### **2. 整体描述 (Overall Description)**

#### **2.1 产品远景 (Product Perspective)**
IMList 是一款独立的、以用户为中心的影视剧集管理工具。它致力于成为市场上最简洁、专注且高效的个人影视追踪解决方案。产品的核心设计哲学是‘功能强大，体验无扰’。它通过提供精准的进度追踪、及时的更新提醒和直观的界面设计，帮助用户轻松管理其个人影视库，解决‘忘记进度’和‘错过更新’的核心痛点。与功能臃肿、社交泛滥的应用不同，IMList 专注于核心管理功能，为用户提供一个纯粹、流畅且可靠的工具，使其成为用户在海量影视内容中进行个人化管理的‘第二大脑’。

#### **2.2 用户特征 (User Classes and Characteristics)**
产品的目标用户主要分为以下三类，他们共同的深层需求是对个人观影行为实现有序、高效的管理和掌控。

*   **2.2.1 效率至上的“剧集管理者” (The Efficient Show Manager):**
    *   **特征:** 这是产品的核心用户群。他们通常同时追踪多部连载剧集，追求效率，希望最大化减少在“回忆进度”和“查询更新”上浪费的时间。
    *   **核心需求:** 精准到单集的观看进度追踪；可靠、自动化的新集播出提醒；一个简洁、无干扰的界面。
*   **2.2.2 热爱数据的“观影档案家” (The Data-Loving Archivist):**
    *   **特征:** 这类用户视观影为一种可记录、可分析的个人爱好。他们热衷于量化自己的观影历史，并从中获得成就感。数据的完整性和可控性至关重要。
    *   **核心需求:** 将观影记录统一存档；可视化的个人观影数据统计；数据的可迁移性。
*   **2.2.3 跨平台内容的“中央收藏家” (The Central Collector):**
    *   **特征:** 这类用户订阅了多个流媒体服务，内容消费渠道广泛。他们的“想看列表”分散在各个平台，难以进行统一、高效的管理。
    *   **核心需求:** 一个能聚合所有平台内容的中央收藏夹；方便地查询某部剧集在哪个平台可以观看；灵活的列表管理功能。

#### **2.3 运行环境 (Operating Environment)**
*   **目标操作系统:** Android
*   **最低系统版本要求:** Android 8.0 Oreo
*   **支持的设备:** 应用程序需要同时适配智能手机与平板电脑。界面布局和交互应能根据屏幕尺寸和方向（横屏/竖屏）进行自适应调整。

---

### **3. 系统功能需求 (System Features)**
*(注：功能点已根据最终讨论顺序重新编号)*

#### **3.1 个人化影视库 (Personalized Library)**
*   **3.1.1 添加剧集:** 系统必须在“搜索结果页”的每个条目上和“剧集详情页”的显著位置，提供“添加至片库”的按钮。
*   **3.1.2 默认状态:** 用户将一部新剧集添加至片库时，该剧集的默认状态应自动设置为“想看”。
*   **3.1.3 列表信息展示:** 影视库列表页的每个剧集条目应以卡片形式展示。
    *   **“在看”列表卡片**需包含：海报、剧名、首播年份、观看进度条、精确进度文本 (如 S02E08/S02E12)、下一集播出信息。
    *   **“想看”列表卡片**需包含：海报、剧名、首播年份、剧集状态（连载中/已完结）、下一集/季播出信息、主流评分（如TMDB评分）。
    *   **“已看”列表卡片**需包含：海报、剧名、首播年份、观看完成日期。
*   **3.1.4 批量管理功能:** 系统应提供一个“选择模式”，允许用户在影视库中勾选多个剧集条目。在选择模式下，用户应可以对所有选中的条目执行以下批量操作：批量修改状态、批量移除、批量添加到自定义列表。

#### **3.2 状态管理 (Status Management)**
*   **3.2.1 智能化完成状态 (Smart Completion):** 当用户在“精细化进度追踪”界面将一部已知结局的剧集的最后一集标记为“已看”时，系统必须自动触发一个确认提示，询问用户是否要将整部剧标记为“已看”。
*   **3.2.2 便捷的操作入口:**
    *   **影视库列表页:** 用户应能通过在剧集卡片上执行“长按”操作，唤出一个包含状态切换选项的快捷菜单。
    *   **剧集详情页:** 在剧集详情页的显著位置，必须提供一个状态选择器，允许用户点击后在三种状态之间进行切换。

#### **3.3 精细化进度追踪 (Detailed Progress Tracking)**
*   **3.3.1 界面结构:** 进度追踪界面应采用顶部标签页设计，用于在不同“季”(Season)之间进行切换。每个单集条目应默认展示集数编号、单集标题、播出日期，并提供可展开区域以显示单集简介。
*   **3.3.2 标记功能:**
    *   **单集标记:** 每个单集条目旁需提供一个复选框，允许用户独立标记/取消标记该集的“已看”状态。
    *   **批量标记 - 整季:** 每个季的列表顶部需提供“标记本季为已看”的功能按钮。
    *   **批量标记 - 标记至此:** 系统需支持长按操作。当用户长按某一集的复选框时，系统应自动将从第一集到该集的所有集数标记为“已看”（需用户二次确认）。
*   **3.3.3 特别篇处理:** 所有非正规季的“特别篇”剧集，应被统一归类到一个名为“Specials”的独立标签页下进行展示和管理。
*   **3.3.4 智能焦点与引导 (Intelligent Focus and Guidance):** 当用户进入“在看”剧集的进度追踪页面时，列表视图必须自动滚动到第一集未被标记为“已看”的剧集位置，并且该集必须有独特的视觉样式（如背景高亮或标签）使其清晰可辨。

#### **3.4 新集播出提醒 (New Episode Alerts)**
*   **3.4.1 提醒规则:** 系统默认对所有用户设置为“在看”状态的剧集，在每一集新内容播出时发送提醒。用户可以为“想看”列表中的剧集手动开启“开播提醒”。
*   **3.4.2 提醒形式:** 提醒主要通过操作系统的推送通知（Push Notification）发送，文案需包含剧集名和季集号。
*   **3.4.3 用户控制与设置:** 应用的设置菜单中必须提供一个用于开启/关闭所有播出提醒的总开关。每个剧集的详情页都必须提供一个独立的提醒开关，允许用户为特定剧集覆盖全局设置。
*   **3.4.4 系统可靠性:** 后台系统必须具备针对播出提醒的去重逻辑和延迟校验机制，以防止因数据源错误导致重复或错误的通知。
*   **3.4.5 通知可追溯性:** 在“播出日历”视图中，对于系统已成功发送播出提醒的剧集条目，必须有明确的视觉标记（如图标）。

#### **3.5 播出日历 (Calendar View)**
*   **3.5.1 视图与布局:** 日历功能需提供两种视图模式：“议程列表视图”(Agenda View)和“月视图”(Monthly View)，并以议程列表为默认视图。
*   **3.5.2 信息展示:** 每个剧集卡片需包含：剧集海报缩略图、剧集名称、季集号、播出平台信息，以及提醒状态标记。
*   **3.5.3 数据范围与筛选:** 日历默认显示用户“在看”和“想看”（即将开播）列表中的所有相关剧集。系统需提供筛选功能，允许用户按剧集状态过滤日历内容。
*   **3.5.4 交互设计:** 点击日历中的任一剧集条目，必须直接跳转至该剧集的进度追踪页面，并自动定位及高亮显示被点击的对应剧集。
*   **3.5.5 整合观看历史 (Viewing History Integration):** 日历视图必须同时支持展示未来播出计划和过往观看记录。当用户将某一集标记为“已看”时，系统需在日历的对应日期上，明确展示该条目并使用独特的视觉标识。
*   **3.5.6 离线可用性 (Offline Availability):** 应用必须在本地设备上缓存日历数据。在设备离线的状态下，日历功能必须能够从缓存中加载并完整展示，并提供清晰的离线模式视觉提示。

#### **3.6 发现与浏览 (Discovery & Browsing)**
*   **3.6.1 “发现”页面:** 应用需提供一个独立的“发现”主页面，内容必须以多个主题式横向滚动列表（泳道）的形式组织，必须至少包含“时下流行”、“高分推荐”、“即将播出”等核心泳道。
*   **3.6.2 剧集详情页 (Details Page):** 点击“发现”页中的任何剧集，必须能跳转到一个标准化的剧集详情页，全面展示从数据源获取的影视信息，并提供核心的用户操作入口（如“添加到片库”）。
*   **3.6.3 相关推荐:** 在每个“剧集详情页”的底部，必须增加一个“相关推荐”模块，调用第三方数据源的相似剧集API，展示与当前剧集相似的其他作品。

#### **3.7 自定义列表 (Custom Lists)**
*   **3.7.1 列表管理:** 应用需提供一个专门的“我的列表”管理页面，用户可以创建新列表（含名称和可选描述），并可进行重命名、编辑描述和删除操作。
*   **3.7.2 添加内容:** 系统需支持从主影视库批量选择剧集添加，并在剧集详情页提供单次添加的入口。
*   **3.7.3 内部排序:** 自定义列表默认支持用户手动拖拽排序条目，并提供按剧集名、播出年份、评分等自动排序的选项。
*   **3.7.4 信息展示:** 自定义列表中的剧集卡片，必须与主影视库的卡片设计保持一致，并能同样展示动态数据。
*   **3.7.5 列表统计:** 系统必须为每个自定义列表提供一个独立的统计模块，至少包含：条目总数与已看比例、总观看时长、以及条目平均评分。
*   **3.7.6 生成式图片分享 (Generated Image Sharing):**
    *   每个自定义列表需提供“分享”功能，触发后需先展示一个预览与自定义界面，允许用户选择模板和配置内容。
    *   最终生成的图片必须包含清晰的 IMList 品牌标识，以及一个可扫描的、指向应用商店页面的二维码。
    *   图片生成后，系统需自动调用操作系统的原生分享接口。

#### **3.8 个人观看统计 (Viewing Statistics)**
*   **3.8.1 数据指标与内容:** 统计页面需展示顶层的“英雄数据”（总时长、总集数、总剧集数），并通过条形图展示核心数据分布（类型、平台等），并提供趣味性榜单（最长剧集Top 5、最常看演员Top 5）。
*   **3.8.2 界面与交互:** 页面应采用仪表盘式的卡片布局，并提供时间范围筛选功能，允许用户查看“全部时间”或特定“年度”的统计报告。
*   **3.8.3 年度报告分享:** 当查看年度报告时，必须提供“分享”功能，调用“生成式图片分享”模块，使用专属的信息图模板生成可视化的年度观影总结图片。
*   **3.8.4 里程碑提醒:** 系统应在用户达成特定的观看里程碑时，通过轻量化的方式予以祝贺。
*   **3.8.5 数据校准与管理:** 系统需能识别并处理短时间内大量标记历史记录的行为，并允许用户选择是否将这些记录从详细的时间统计中排除。系统必须允许用户手动修改剧集的“完成日期”。

#### **3.9 系统核心行为：离线操作与数据同步**
*   **3.9.1 离线优先:** 应用的核心数据（影视库、观看进度、自定义列表、日历信息）必须本地缓存。用户在离线时必须能够无障碍地浏览所有缓存数据，并能执行修改性操作（操作进入本地队列）。
*   **3.9.2 自动同步:** 当设备重新连接到网络时，应用必须在后台自动、静默地启动同步流程，提交离线操作并从服务器拉取最新数据。

#### **3.10 全局搜索与快速操作 (Global Search & Quick Actions)**
*   **3.10.1 统一入口与混合搜索:** 应用需在主要界面提供一个全局搜索入口，搜索结果必须混合展示来自“用户个人片库”和“在线公共数据库”的内容，并进行清晰的分区。
*   **3.10.2 集成快速操作:** 在搜索结果的每个条目旁，必须根据该条目的状态，提供上下文感知的“快速操作”按钮。执行快速操作后，用户无需离开搜索结果页。
*   **3.10.3 技术实现要点:** 本地搜索必须使用支持全文搜索（FTS5）的数据库方案（如 Room）。搜索逻辑必须采用异步方式，通过响应式编程模型对本地和远程数据流进行合并与展示。

#### **3.11 商业化：广告与高级订阅 (Monetization: Ads & Premium Subscription)**
*   **3.11.1 广告策略（免费版）:** 集成Google AdMob，策略性地使用多种广告形式，包括原生广告、横幅广告和插屏广告。广告的展示频次和时机应经过精心设计，以寻求收益和用户体验的平衡点。
*   **3.11.2 IMList高级版:** 必须为用户提供一个明确的付费订阅选项，可提供按月/年订阅和永久买断的选项。核心权益为“完全移除所有广告”，并作为未来高级功能的解锁方式。
*   **3.11.3 支付与订阅管理:** 集成Google Play Billing Library，必须提供“恢复购买”功能，确保用户在更换设备后能恢复其高级版状态。

---

### **4. 非功能性需求 (Non-functional Requirements)**

#### **4.1 性能需求 (Performance)**
*   **4.1.1 应用启动速度:** 冷启动时间必须小于1.5秒；热启动时间必须小于0.5秒。
*   **4.1.2 界面流畅度:** 所有涉及用户滚动操作的列表，其滚动帧率必须稳定在 60 fps。
*   **4.1.3 数据加载时间:** 本地数据操作必须在200毫秒内完成渲染；正常网络下的在线搜索返回结果时间必须小于2秒。
*   **4.1.4 资源消耗:** 首个版本的安装包（APK）大小应力争控制在50MB以内。不允许出现后台异常耗电行为。

#### **4.2 安全与隐私需求 (Security & Privacy)**
*   **4.2.1 用户认证:** 优先支持Google登录。对于邮箱/密码注册，密码在后端存储时，必须采用Bcrypt算法进行加盐哈希存储。
*   **4.2.2 数据传输安全:** 应用客户端与服务器之间的所有API通信，必须强制使用 HTTPS (TLS 1.2 或更高版本) 协议。
*   **4.2.3 本地数据存储安全:** 依赖Android平台的应用沙箱机制进行基础保护，v1.0阶段无需对本地数据库进行额外加密。
*   **4.2.4 凭证与API密钥安全:** 严禁将任何第三方服务的API密钥硬编码在客户端的源代码中。应通过服务器端管理或安全的编译时注入方式进行配置。
*   **4.2.5 用户隐私与数据管理:** 应用内必须提供易于访问的隐私政策、允许用户将核心数据导出为标准格式（如JSON）的功能、以及明确的账户删除流程。

#### **4.3 平台合规性需求 (Platform Compliance)**
*   **高优先级:** 本章节的要求具有高优先级，必须严格遵守。
*   **4.3.1 Google Play“数据安全”规范:** 在Google Play管理中心所填写的“数据安全”信息，必须在每次发布前进行审核，确保与应用实际行为完全一致。
*   **4.3.2 Google Play权限请求规范:** 严格遵循最小权限和情景化请求原则。对于所有非必要权限，应用必须进行优雅降级处理，确保核心功能可用。
*   **4.3.3 Google Play账户删除规范:** 应用内和应用外（如网页）均需提供清晰、易于发现的账户及关联数据删除功能。

#### **4.4 可用性需求 (Usability)**
*   **4.4.1 易学性:** 应用的整体设计语言、导航模式和控件样式，必须严格遵循 Google Material Design 3 的最新规范。
*   **4.4.2 效率:** 高频任务的操作路径必须被设计得尽可能的短。应优先使用非侵入性的方式提供反馈，避免滥用打断心流的模态弹窗。
*   **4.4.3 可访问性 (a11y):** 必须提供对Android原生辅助功能的全面支持，包括为所有无标签控件提供内容描述、支持动态字体、保证触摸目标尺寸不小于48x48dp、以及确保色彩对比度满足WCAG 2.1 AA级标准。
*   **4.4.4 系统反馈:** 任何用户操作必须在100毫秒内给出视觉反馈。耗时操作必须提供清晰的加载指示器。操作完成后必须有明确的结果提示。
*   **4.4.5 微交互与情感化设计:** 关键的用户操作和状态转换必须伴随有意义、流畅的过渡动画。在完成重要操作时，应提供克制的触觉反馈。所有空状态页面，都必须有专门的引导性插图和文字说明。

#### **4.5 可靠性需求 (Reliability)**
*   **4.5.1 系统稳定性:** 无崩溃会话率必须高于 99.8%；无ANR会话率也应高于 99.8%。
*   **4.5.2 数据一致性:** 需制定明确的数据同步冲突解决策略，v1.0版本采用“后来者覆盖 (Last-Write-Wins)”策略。
*   **4.5.3 容错能力:** 客户端在处理API或网络错误时绝不能崩溃，必须能捕获所有相关异常并向用户显示友好的错误提示。
*   **4.5.4 灾难恢复:** 后台服务器必须有定期的、自动化的数据库备份机制，并保留至少14天的数据。
*   **4.5.5 主动监控与可维护性:** 应用必须集成远程错误监控服务（如Firebase Crashlytics）和性能监控服务。开发过程需建立结构化的日志策略，并遵循现代化的软件架构和测试实践以保证代码的长期可维护性。

---

### **5. 外部接口需求 (External Interface Requirements)**

#### **5.1 用户界面 (User Interfaces)**
*   **5.1.1 设计语言与风格:** 严格遵循 Google Material Design 3，必须提供亮色和暗色主题，并能根据系统设置自动切换。
*   **5.1.2 屏幕布局与适配:** 必须采用响应式布局。对于平板电脑等大屏幕设备，必须采用“列表-详情”等多窗格布局进行优化。
*   **5.1.3 导航模式:** 主要层级导航必须采用底部导航栏 (Bottom Navigation Bar) 的模式。
*   **5.1.4 系统集成与快捷方式:** 应用必须提供静态快捷方式（长按图标显示“搜索”等）和动态快捷方式（根据用户行为动态显示最近访问的剧集）。

#### **5.2 硬件接口 (Hardware Interfaces)**
*   **5.2.1 所需硬件:** 应用需与设备的屏幕显示、网络接口（Wi-Fi/蜂窝）、震动马达进行交互。
*   **5.2.2 智能网络管理策略:**
    *   **节省数据选项:** 应用必须能感知网络类型，并为用户提供一个“仅在Wi-Fi下加载图片”的设置选项。
    *   **智能预缓存:** 应用应为用户提供一个选项，允许其仅在设备连接到Wi-Fi且处于充电状态时，在后台执行数据预缓存和内容更新任务。

#### **5.3 软件接口 (Software Interfaces)**
*   **5.3.1 影视信息数据源:** The Movie Database (TMDB) API。
*   **5.3.2 “在哪看”信息数据源:** 利用TMDB API集成的JustWatch数据。
*   **5.3.3 用户认证服务:** Google Sign-In for Android API。
*   **5.3.4 远程错误监控与分析服务:** Firebase Crashlytics API / Sentry API。
*   **5.3.5 广告变现与中介平台:** 以 Google AdMob SDK 作为主要的广告SDK和广告中介平台。在AdMob中介管理下，需准备好集成 Meta Audience Network, AppLovin, Unity Ads 等其他主流广告网络SDK。
*   **5.3.6 支付与订阅服务:** Google Play Billing Library。

---
**[文档结束]**