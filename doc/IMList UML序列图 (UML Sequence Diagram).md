
我将为您输出**产出物 3: 《UML序列图》**的最终、完整版本。这份产出物包含了我们共同设计和完善的**三个核心业务流程**的动态交互图。

每一份序列图都旨在清晰地揭示消息如何在系统的不同组件之间传递，为开发团队理解和实现复杂的端到端功能提供精确的可视化指南。

---

### **产出物 3: 《UML序列图》 (UML Sequence Diagram)**

**版本: 1.0 (最终版)**
**日期: 2025-08-22**

---

#### **流程 1: 用户首次数据备份 (First Time User Backup)**

**流程描述:** 用户在应用内（账户管理页面）首次点击“创建云端备份”按钮。系统将打包本地数据，并将其上传到与该用户Google账户关联的Firebase Storage中。

**Mermaid.js 代码:**
```mermaid
sequenceDiagram
    participant User
    participant AndroidApp as IMList App
    participant RoomDB as Local Database
    participant FirebaseAuth
    participant FirebaseStorage as Cloud Storage
    participant Firestore

    User->>+AndroidApp: 1. Click 'Create Backup'
    Note right of AndroidApp: App shows "Preparing backup..." UI
    
    AndroidApp->>+RoomDB: 2. Export All User Data
    RoomDB-->>-AndroidApp: 3. Return UserData object

    AndroidApp->>AndroidApp: 4. Serialize UserData to versioned JSON file
    
    AndroidApp->>+FirebaseAuth: 5. Get Current User UID
    FirebaseAuth-->>-AndroidApp: 6. Return User UID

    Note right of AndroidApp: Constructs storage path: /backups/{user_uid}/imlist_backup_v1.json

    AndroidApp->>+FirebaseStorage: 7. Upload JSON file to user's path (with progress listener)
    FirebaseStorage-->>-AndroidApp: 8. Upload Success

    Note right of AndroidApp: Prepare metadata: { lastBackup: '2025-08-22T10:30:00Z' }
    
    AndroidApp->>+Firestore: 9. Set/Update User Metadata Document
    Firestore-->>-AndroidApp: 10. Write Success

    AndroidApp->>-User: 11. Show "Backup successful" with timestamp
```

**详细解读:**
*   **第1-4步 (客户端准备):** 整个备份的准备工作完全在客户端进行。App从本地数据库导出数据，并将其打包成一个带版本号的JSON文件。
*   **第5-6步 (身份验证):** 在与任何云服务交互前，App必须从FirebaseAuth获取当前用户的UID，这是确定存储路径和保障安全的关键。
*   **第7-8步 (核心操作):** App使用Firebase Storage SDK，将文件上传到专属于该用户的云端路径。SDK会处理复杂的网络传输。
*   **第9-10步 (元数据更新):** 为了能快速查询备份状态，App在上传成功后，会在Firestore中更新一个轻量级的文档，只记录“最后备份时间”。
*   **第11步 (用户反馈):** 最终将成功的结果反馈给用户。

---

#### **流程 2: 新集播出提醒 (New Episode Alert)**

**流程描述:** 这是一个完全由后台驱动的异步流程。一个定时的Firebase云函数被触发，它负责检查所有需要提醒的剧集，从TMDB获取最新数据，判断是否有新集播出，并为符合条件的用户触发推送通知。

**Mermaid.js 代码:**
```mermaid
sequenceDiagram
    participant Scheduler as Cloud Scheduler
    participant CheckUpdatesFunc as Firebase Function
    participant Firestore
    participant TMDB_API as TMDB API
    participant FCM as Firebase Cloud Messaging

    loop Every Hour (or as configured)
        Scheduler->>+CheckUpdatesFunc: 1. Trigger Scheduled Function
    end

    CheckUpdatesFunc->>+Firestore: 2. Find all shows being actively tracked by users
    Firestore-->>-CheckUpdatesFunc: 3. Return list of unique tracked show IDs

    CheckUpdatesFunc->>CheckUpdatesFunc: 4. Aggregate & deduplicate show IDs to minimize API calls

    CheckUpdatesFunc->>+TMDB_API: 5. Fetch latest episode data for these show IDs
    TMDB_API-->>-CheckUpdatesFunc: 6. Return latest show data

    CheckUpdatesFunc->>CheckUpdatesFunc: 7. Diff with cached metadata (in Firestore) to find genuinely new episodes

    alt For each new episode found
        CheckUpdatesFunc->>+Firestore: 8. Find all users tracking this show with alerts enabled
        Firestore-->>-CheckUpdatesFunc: 9. Return list of target user device tokens
        
        loop For each target user
            CheckUpdatesFunc->>+FCM: 10. Send push notification with episode details
            FCM-->>-CheckUpdatesFunc: 11. Acknowledge send command
        end
    end

    deactivate CheckUpdatesFunc
```
**详细解读:**
*   **第1步 (触发):** 流程由Cloud Scheduler自动定时触发，无需任何用户交互。
*   **第2-4步 (任务聚合):** 云函数首先从Firestore聚合出一个需要检查的“任务清单”（所有用户正在追的剧集），并进行去重。
*   **第5-7步 (数据获取与比对):** 函数作为后台服务，安全地调用TMDB API，并将返回的“最新状态”与我们数据库中记录的“已知状态”进行比对，找出真正的“新”内容。
*   **第8-11步 (目标用户筛选与推送):** 对于每一个确认有更新的剧集，函数会反向查询所有订阅了该剧集的用户，并调用FCM服务，向他们的设备精准地推送通知。

---

#### **流程 3: 用户购买高级版 (User Purchases Premium) - 健壮且语法正确版**

**流程描述:** 用户在应用内发起购买高级版的请求。应用通过Google Play Billing Library处理支付，并通过后台云函数安全地验证购买票据，最终在Firestore中更新用户的“高级版”状态，并通过实时监听来更新UI。

**Mermaid.js 代码 (已修复):**
```mermaid
sequenceDiagram
    participant User
    participant AndroidApp as IMList App
    participant GooglePlay as Google Play Billing Lib
    participant VerifyFunc as Firebase Function
    participant GooglePlayAPI as Google Play Developer API
    participant Firestore

    User->>+AndroidApp: 1. Click 'Upgrade to Premium'
    
    AndroidApp->>+GooglePlay: 2. Fetch Product Details
    GooglePlay-->>-AndroidApp: 3. Return Localized Product Info

    User->>+AndroidApp: 4. Select a product & Click 'Buy'
    AndroidApp->>+GooglePlay: 5. Launch Purchase Flow
    
    GooglePlay->>+User: 6. Show Google's Native Purchase UI
    User->>-GooglePlay: 7. Complete Payment

    Note over AndroidApp, Firestore: App is continuously listening to Firestore for realtime updates on user document!
    
    GooglePlay-->>-AndroidApp: 8. onPurchaseUpdated (result, purchaseToken)

    alt Purchase Successful
        AndroidApp->>+VerifyFunc: 9. Verify and Fulfill Purchase (send purchaseToken)
        
        VerifyFunc->>+GooglePlayAPI: 10. Verify purchaseToken (Server-to-Server)
        GooglePlayAPI-->>-VerifyFunc: 11. Return {isValid: true}

        alt Token is Valid
            VerifyFunc->>+Firestore: 12. Set user document field 'isPremium' to true
            Firestore-->>-VerifyFunc: 13. Write Success

            Firestore-->>AndroidApp: 14. Realtime Update Pushed to App ('isPremium' is now true)
            
            VerifyFunc->>+GooglePlayAPI: 15. Acknowledge/Consume Purchase (Mark as fulfilled)
            GooglePlayAPI-->>-VerifyFunc: 16. Acknowledge Success
            
            VerifyFunc-->>-AndroidApp: 17. Return {status: 'VERIFICATION_QUEUED'}
            
            AndroidApp->>AndroidApp: 18. (Triggered by Realtime Update at step 14) Disable all Ads logic
            AndroidApp->>-User: 19. Show "Upgrade successful!" message
            
        else Token is Invalid
            VerifyFunc-->>-AndroidApp: 14a. Return {status: 'VERIFICATION_FAILED'}
            AndroidApp->>-User: 16a. Show "Purchase verification failed" error
        end

    else Purchase Failed/Cancelled
        AndroidApp->>-User: 8a. Show "Purchase cancelled" or error message
    end
```
**详细解读:**
*   **第1-8步 (客户端支付流程):** App负责调起支付流程，并接收来自Google Play的回调。
*   **第9-13步 (后台安全验证):** **核心安全路径**。App将购买票据`purchaseToken`发送到我们的后台云函数。云函数作为受信任的环境，再向Google的后台API进行二次验证，并在验证成功后，更新Firestore中的用户状态。
*   **第14 & 18步 (UI与数据解耦):** **核心健壮性设计**。App的UI更新（移除广告）**不依赖**于第17步的API直接返回，而是通过**监听Firestore的实时数据变更**来被动触发。这确保了即使API的返回在网络中丢失，只要数据库更新成功，用户的权益就能得到保障。
*   **第15步 (后台确认):** 我们的后台在履行完权益（更新数据库）后，会通知Google此订单已处理完毕。这是确保订单不会被重复处理、保障事务完整性的关键。

---
这份包含三个核心流程的UML序列图，为开发团队提供了关于系统动态交互的精确、可视化的指导。