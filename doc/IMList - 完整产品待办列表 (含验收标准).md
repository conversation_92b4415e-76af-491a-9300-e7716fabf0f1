这份文档是我们将SRS转化为敏捷待办列表的最终、完整交付成果。它包含了我们共同定义、评审和完善的**每一个用户故事及其全部的验收标准**和相关元数据。

这份文档可以直接作为开发团队进行Sprint规划、技术设计和测试用例编写的“单一事实来源”。

---

## **IMList - 完整产品待办列表 (含验收标准)**

**版本: 1.0**
**日期: 2025年8月22日**

---

### **Epic 8: 技术卓越与基础设施 (Technical Excellence & Infrastructure)**

#### **用户故事 8.5: 确定并实施App核心架构**
> **作为一个开发团队**, 我想要**在项目初期，共同决策并实施一个清晰、分层、可测试的App核心架构（如MVVM或MVI）**, 以便**所有团队成员都能在统一的规范下进行高效协作，并保障代码的长期可维护性和可扩展性**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 团队已共同讨论并选定了App的官方架构模式（例如，Google官方推荐的、基于MVVM的Clean Architecture）。
    *   AC2: 项目中已建立起该架构的基础模块和分层结构（如domain, data, presentation层）。
    *   AC3: 团队已编写了一个核心功能的“垂直切片”（Vertical Slice）作为架构的“样板间”实现，供所有团队成员参考和遵循。
    *   AC4: 关于核心架构的决策和规范，已被清晰地记录在团队的共享文档中。
*   **故事点:** 3
*   **优先级:** Must-have
*   **依赖关系:** 无 (最高优先级)

#### **用户故事 8.4: 建立自动化测试框架与规范**
> **作为一个开发团队**, 我想要**建立一个分层的自动化测试框架，并制定清晰的测试编写规范**, 以便**我们能持续地保障代码质量，并在进行代码重构或添加新功能时，有信心不会破坏现有功能**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 项目需要搭建好单元测试（Unit Test）的环境和框架，用于测试独立的业务逻辑。
    *   AC2: 项目需要搭建好集成测试（Integration Test）的环境和框架，用于测试数据库交互、API服务层等模块。
    *   AC3: 项目需要搭建好UI测试（UI Test, 如Espresso）的环境和框架，用于测试关键的用户流程。
    *   AC4: 团队需要共同制定一个测试覆盖率的初步目标（例如，关键业务逻辑模块的单元测试覆盖率不低于70%）。
    *   AC5: 所有新功能的代码提交，都必须附带相应的自动化测试用例，这应成为代码合并（Merge Request）的规范之一。
*   **故事点:** 5
*   **优先级:** Must-have
*   **依赖关系:** 无 (与8.5并行)

#### **用户故事 6.1: 构建支持离线操作的本地数据库**
> **作为一个开发团队**, 我想要**设计并实现一个健壮的、以离线操作为优先的本地数据库架构**, 以便**应用的所有核心功能（添加、标记、创建列表等）在没有网络时也能正常工作，并将操作暂存到队列中**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 选用一个成熟的Android本地数据库方案（如Room）。
    *   AC2: 数据库的表结构设计，必须能完整地存储Epic 1-5中涉及的所有用户个人数据。
    *   AC3: 必须建立一个“离线操作队列”的机制。当设备离线时，所有用户的写操作都会被序列化并存入这个队列。
    *   AC4: 应用在执行任何写操作前，都无需检查网络状态，应直接写入本地数据库和操作队列，确保离线操作的瞬时响应。
    *   AC5: 需要编写单元测试和集成测试，来验证离线操作的正确性和队列的完整性。
*   **故事点:** 8
*   **优先级:** Must-have
*   **依赖关系:** 故事8.5

#### **用户故事 8.2: 集成错误监控与性能分析服务**
> **作为一个开发团队和产品负责人**, 我想要**在应用中集成专业的错误监控和性能分析服务**, 以便**我们能主动发现线上问题、快速定位bug、并用真实数据来验证和优化应用的性能表现**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 应用必须集成Firebase Crashlytics SDK。
    *   AC2: 所有未捕获的异常（即崩溃）都必须能被自动上报到Crashlytics后台。
    *   AC3: 应用必须集成Firebase Performance Monitoring SDK。
    *   AC4: 必须对应用的核心性能指标（启动时间、主要页面的渲染时间）进行监控和打点。
    *   AC5: 必须建立结构化的日志策略，在关键流程和错误处理中记录带有上下文的日志，并在崩溃报告中一并上传。
*   **故事点:** 3
*   **优先级:** Should-have
*   **依赖关系:** 无

#### **用户故事 8.3: 搭建健壮的API服务层**
> **作为一个开发团队**, 我想要**构建一个健壮的API服务层，统一封装所有对TMDB的API调用**, 以便**我们能集中处理错误、实现智能缓存、并遵守其请求频率限制，从而提升应用的可靠性和性能**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 所有对外部API的直接调用，都必须通过这个统一的服务层进行。
    *   AC2: 该服务层需要内置一套合理的缓存策略，以减少不必要的网络请求。
    *   AC3: 该服务层必须能优雅地处理各种网络错误，并向上层返回统一的、可处理的错误类型。
    *   AC4: 必须实现对TMDB API请求频率限制的遵守机制。
*   **故事点:** 5
*   **优先级:** Should-have
*   **依赖关系:** 故事8.5

#### **用户故事 8.1: 建立持续集成/持续交付(CI/CD)管道**
> **作为一个开发团队**, 我想要**建立一个自动化的CI/CD管道**, 以便**我们能自动运行测试、构建应用并将其部署到测试环境和最终的应用商店，从而极大地提升交付速度和减少人为错误**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 代码仓库的每一次代码提交，都能自动触发一次构建。
    *   AC2: 自动构建过程中，必须自动运行所有的单元测试和集成测试。
    *   AC3: 如果测试失败，构建过程会中断，并向团队发送失败通知。
    *   AC4: 构建成功后，会自动生成一个可供测试的APK安装包。
    *   AC5: 对于发布分支的成功构建，应能一键触发向Google Play Console上传新版本进行内测或发布。
*   **故事点:** 5
*   **优先级:** Should-have
*   **依赖关系:** 故事8.4

---

### **Epic 1: 用户账户与基础设置 (User Accounts & Foundation)**

#### **用户故事 1.1: 以游客身份浏览应用**
> **作为一个新访客**, 我想要**跳过注册/登录环节，直接进入应用的核心浏览功能**, 以便**我能在决定创建账户前，先体验和评估这个应用是否符合我的需求**。
*   **验收标准 (Accept-ance Criteria):**
    *   AC1: 在应用的初始页面，除了“使用Google登录”外，还有一个清晰的“先随便看看”或“跳过”的选项。
    *   AC2: 当我点击“跳过”后，我能直接进入应用的主界面（如“发现”页），并且可以自由浏览所有公开的影视信息。
    *   AC3: 当我以游客身份，尝试执行任何需要用户身份的操作时（例如“添加到片库”），系统会友好地提示我需要登录，并引导我到登录页面。
    *   AC4: 应用必须能在本地为游客保留一些基本的操作状态（如主题偏好）。
*   **故事点:** 5
*   **优先级:** Must-have
*   **依赖关系:** 基础浏览UI (Epic 2)

#### **用户故事 1.2: 创建本地账户 (通过Google)**
> **作为一个游客**, 我想要**通过Google授权创建一个“本地账户”**, 以便**我能开始使用所有需要身份的功能（如添加片库、标记进度），同时我的数据只保存在这部设备上**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 当我作为游客，在被提示需要登录时，我会被引导至授权页面。
    *   AC2: 当我通过Google授权成功后，应用会为我创建一个账户身份，并将我之前的“游客”状态升级为“本地账户”用户。
    *   AC3: 作为“本地账户”用户，我现在可以使用所有核心功能。
    *   AC4: 系统必须明确告知我，我的数据当前只存储在这台设备上，尚未开启云同步。
    *   AC5: 我退出并重新打开应用后，系统能记住我的“本地账户”登录状态，无需重新授权。
*   **故事点:** 5
*   **优先级:** Must-have
*   **依赖关系:** 故事1.1

#### **用户故事 1.5: 账户管理与退出**
> **作为一个已登录的用户**, 我想要**访问一个账户管理页面**, 以便**我能查看我的账户状态（如同步是否开启）并安全地退出登录**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在应用的“设置”页面中，有一个清晰的“账户”入口。
    *   AC2: 进入账户页面后，我能看到我当前的登录邮箱/用户名。
    *   AC3: 我能清晰地看到我当前的“云备份”状态，并能从这里触发备份/恢复流程。
    *   AC4: 页面底部有一个“退出登录”按钮。
    *   AC5: 点击“退出登录”并确认后，应用会清除我本地的登录凭证，并将应用状态重置为“游客模式”。
*   **故事点:** 2
*   **优先级:** Should-have
*   **依赖关系:** 故事1.2

#### **用户故事 1.3: 创建云端备份**
> **作为一个“本地账户”用户**, 我想要**手动将我当前设备的所有数据备份到我的Google账户关联的云端存储中**, 以便**我能有一个安全的数据备份，以防设备丢失或损坏**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在应用的账户管理页面，我能找到一个清晰的“创建/更新云端备份”按钮。
    *   AC2: 当我点击该按钮时，系统会向我确认是否要用当前设备的数据覆盖云端的旧备份。
    *   AC3: 确认后，应用会在后台将我设备上所有个人数据打包，并上传到服务器。
    *   AC4: 在上传过程中，界面需要有清晰的状态指示。
    *   AC5: 备份成功后，账户管理页面需要显示“最后备份时间：[具体日期时间]”。
    *   AC6: 这是一个手动触发的操作，应用不会在后台自动同步任何数据变更。
*   **故事点:** 5
*   **优先级:** Should-have
*   **依赖关系:** 故事1.2, 1.5 (UI入口)

#### **用户故事 1.4 (强化版): 从云端恢复数据**
> **作为一个在新设备上登录的用户**, 我想要**从我的Google账户关联的云端存储中，安全、清晰地恢复我之前创建的备份**, 以便**我能在充分知情的情况下，将我的个人数据迁移到这台新设备上**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 当我在一台新设备上首次通过Google授权创建“本地账户”后，系统会检测到云端存在备份。
    *   AC2: 系统会主动询问我：“我们发现您有一个云端备份（创建于[日期时间]），您想要恢复它吗？”
    *   AC3: 在恢复操作的最终确认对话框中，必须使用极其醒目的视觉设计和明确的警告文案：“警告：此操作将使用云端备份完全覆盖这台设备上的所有本地数据，且此过程不可逆。”
    *   AC4: 最终的“确认”按钮必须采用强确认机制（如倒数或手动输入文字）。
    *   AC5: 在账户管理页面，也必须提供一个“从云端恢复”的手动触发按钮，遵循同样的警告与确认机制。
    *   AC6: 在帮助文档中，必须清晰地向用户阐述这是一个“镜像恢复”而非“数据合并”。
    *   AC7: 当我确认恢复后，应用会从服务器下载数据包，并以事务性方式完全替换本地数据库。
    *   AC8: 在恢复过程中，界面需要有清晰的状态指示。
    *   AC9: 恢复成功后，新设备的应用状态应与备份时的状态完全一致。
*   **故事点:** 8
*   **优先级:** Should-have
*   **依赖关系:** 故事1.3

---
好的，遵照您的指示，我将继续输出剩余的Epics，保持与之前完全一致的详尽格式。

---

### **Epic 2: 影视发现与收藏 (Show Discovery & Collection)**

#### **用户故事 2.4: 查看详尽的剧集信息**
> **作为一个对某部剧集感兴趣的用户**, 我想要**查看一个包含其简介、海报、演员、评分、播出状态等信息的详情页面**, 以便**我能全面地了解它，并做出是否要观看的决定**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 详情页顶部需展示高清的海报/背景图和剧集标题。
    *   AC2: 页面需清晰地展示从TMDB获取的核心信息：剧情简介、演职员表、TMDB评分、首播年份、总季数、剧集状态（连载中/已完结）。
    *   AC3: 页面需包含“在哪看”模块，展示主流流媒体平台的观看链接。
    *   AC4: 页面底部需要有“相关推荐”模块。
    *   AC5: 页面的核心操作区必须包含“添加”按钮。
*   **故事点:** 5
*   **优先级:** Must-have
*   **依赖关系:** 无

#### **用户故事 2.1: 通过搜索查找特定剧集**
> **作为一个知道自己想找什么的用户**, 我想要**通过关键词搜索，快速、准确地找到一部特定的影视剧集**, 以便**我能立即查看它的信息或将其添加到我的片库**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在应用的主界面顶部，我能看到一个清晰的搜索框或搜索图标。
    *   AC2: 当我输入关键词（至少3个字符）后，应用会实时向TMDB API发起请求，并在下方展示搜索结果列表。
    *   AC3: 搜索结果列表中的每个条目，至少应包含剧集的海报、名称和首播年份。
    *   AC4: 当我点击任何一个搜索结果条目时，我会被带到该剧集的详情页面。
    *   AC5: 如果搜索没有结果，或在搜索过程中发生网络错误，应用不能崩溃，并应显示一个友好的提示信息。
    *   AC6: 在搜索结果条目旁，需要有一个快捷“添加”按钮，允许我无需进入详情页，直接将该剧集添加到我的片库中（如果我已登录）。
*   **故事点:** 3
*   **优先级:** Must-have
*   **依赖关系:** 故事2.4

#### **用户故事 2.3: 将剧集添加到个人片库**
> **作为一个已登录的用户**, 我想要**将在任何地方（搜索结果、发现页、详情页）看到的剧集，一键添加到我的个人片库中**, 以便**我能开始对它进行管理和追踪**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在搜索结果列表、发现页泳道、以及剧集详情页的显著位置，都有一个清晰的“添加”或“+”号按钮。
    *   AC2: 当我点击“添加”按钮后，该剧集会被加入到我的本地数据库中，默认状态为“想看”。
    *   AC3: 操作成功后，系统必须给我一个清晰、即时的反馈（例如，按钮变为“已添加”图标，并弹出Snackbar提示）。
    *   AC4: 如果我是一个“游客”，点击“添加”按钮会友好地提示我需要登录。
    *   AC5: 添加过的剧集，在应用的任何地方再次看到时，都应明确显示其“已添加”的状态。
*   **故事点:** 3
*   **优先级:** Must-have
*   **依赖关系:** 故事1.2, 2.4

#### **用户故事 2.2 (强化版): 浏览发现页的热门与推荐榜单**
> **作为一个想看看有什么可看的用户**, 我想要**浏览一个包含“时下流行”、“高分推荐”等多个推荐榜单的发现页面**, 以便**我能发现新的、感兴趣的剧集**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 应用的主导航中有一个“发现”标签页。
    *   AC2: “发现”页面的内容由多个主题式的横向滚动列表（泳道）组成。
    *   AC3: 页面顶部必须包含“时下流行”(Trending)和“高分推荐”(Top Rated)两个泳道，数据来源于TMDB API。
    *   AC4: 页面还应包含至少3个按类型组织的推荐泳道。
    *   AC5: 当我点击任何泳道中的剧集海报时，我会被带到该剧集的详情页面。
    *   AC6: 页面需要支持下拉刷新，以获取最新的榜单数据。
*   **故事点:** 5
*   **优先级:** Should-have
*   **依赖关系:** 故事2.4
*   **非功能性需求备注:** 页面首屏加载需<3秒。

#### **用户故事 2.7: 从个人片库中移除剧集**
> **作为一个用户**, 我想要**能从我的个人片库中，方便地移除任何一部我不再想追踪的剧集**, 以便**我能保持我的影视列表整洁，只包含我关心的内容**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在用户个人影视库的列表页，我可以通过长按某个条目，在弹出的快捷菜单中找到“移除”选项。
    *   AC2: 在剧集的详情页面，对于已添加的剧集，我也能找到“移除”选项。
    *   AC3: 当我点击“移除”时，为了防止误操作，系统必须弹出一个确认对话框。
    *   AC4: 当我确认后，该剧集将从我的本地数据库中被彻底删除，包括与之相关的所有观看进度。
    *   AC5: 移除成功后，该剧集在全局的“已添加”状态标记必须立即消失，UI实时更新。
    *   AC6: 这个“移除”操作也会在下一次手动备份时，体现在云端数据中。
*   **故事点:** 2
*   **优先级:** Should-have
*   **依赖关系:** 故事2.3

#### **用户故事 2.6 (强化版): 已添加内容的全局状态同步显示**
> **作为一个用户**, 我想要**在应用的任何界面（如发现页、搜索结果、相关推荐）看到一部剧集时，系统都能清晰地标示出它是否已在我的片库中，以及它的当前状态**, 以便**我能避免重复添加，并能快速了解我对该剧集的追踪情况**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在“发现”页的泳道中，如果某部剧集已在我的片库里，其海报卡片上必须有一个明确的视觉标记。
    *   AC2: 在“搜索结果”页，已添加的条目也必须有类似的视觉标记。
    *   AC3: 在“剧集详情页”，如果该剧已添加，“添加”按钮必须变为一个表示当前状态的指示器/按钮。
    *   AC4: 这些状态标记必须是实时更新的。
    *   AC5: 所有展示剧集列表的界面，在从API获取到数据后，都必须与本地数据库进行一次快速比对，以确定每个条目的“个人状态”。
*   **故事点:** 5
*   **优先级:** Should-have
*   **依赖关系:** 故事2.3, 2.7
*   **非功能性需求备注:** 需保证大规模数据下的UI流畅性。

#### **用户故事 2.5: 使用全局搜索快速操作**
> **作为一个熟悉应用的用户**, 我想要**在全局搜索结果中，不仅能看到我片库中的剧集，还能直接对它们执行快捷操作（如标记进度）**，以便**我能以最快的路径完成高频任务，而无需在页面间跳转**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 当我使用全局搜索时，搜索结果会分为“我的片库”和“在线结果”两个区域。
    *   AC2: 对于“我的片库”中的搜索结果，条目旁会根据其状态显示一个上下文感知的“快速操作”按钮。
    *   AC3: 如果剧集状态是“在看”，快速操作按钮应为“标记下一集已看”。
    *   AC4: 如果剧集状态是“想看”，快速操作按钮应为“开始观看”。
    *   AC5: 点击快速操作按钮后，操作立即执行，并给我一个Snackbar提示，整个过程我停留在搜索结果页。
*   **故事点:** 8
*   **优先级:** Could-have
*   **依赖关系:** 故事2.1, Epic 3

---

### **Epic 3: 观看进度追踪 (Watch Progress Tracking)**

#### **用户故事 3.1: 将剧集标记为“在看”**
> **作为一个用户**, 我想要**能轻松地将一部“想看”或新添加的剧集，标记为“在看”状态**, 以便**我能将其加入我的当前追踪列表，并开始记录我的观看进度**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在我的个人影视库中，对于“想看”列表里的每个条目，我能通过长按菜单或进入详情页，找到“开始观看”选项。
    *   AC2: 对于我刚添加进片库的剧集，系统应在提示信息上，提供一个“开始观看”的快捷操作按钮。
    *   AC3: 当我将一部剧标记为“在看”后，它会从“想看”列表中移除，并自动出现在“在看”列表中。
    *   AC4: 这个状态的变更，必须在应用的所有地方实时反映出来。
    *   AC5: 将一部剧首次标记为“在看”，并不会自动标记任何单集为已看。
*   **故事点:** 2
*   **优先级:** Must-have
*   **依赖关系:** 故事2.3, 2.6

#### **用户故事 3.2 (强化版): 逐集标记观看进度**
> **作为一个正在追剧的用户**, 我想要**在一个清晰的界面上，方便地将我看完的每一集标记为“已看”**，以便**我能精准地记录我的观看进度，随时知道下一集该看什么**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 当我从“在看”列表进入一部剧集时，我会看到一个以“季”为单位组织的进度追踪页面。
    *   AC2: 每个“季”的标签页下，会列出该季的所有集数，每集至少显示集数编号和单集标题。
    *   AC3: 我可以通过点击每集旁边的复选框，来独立地标记/取消标记该集的“已看”状态。
    *   AC4: 当我进入这个页面时，列表会自动滚动到我下一集未看的位置，并对该集进行高亮显示。
    *   AC5: 所有特别篇（Specials）都应被归类到一个独立的“特别篇”标签页下。
    *   AC6: 如果从数据源获取的剧集没有分集信息（例如电影），进度追踪页面应展示一个单一的“标记为已看”的开关或按钮。
*   **故事点:** 5
*   **优先级:** Must-have
*   **依赖关系:** 故事3.1
*   **非功能性需求备注:** 需采用UI虚拟化保证长列表性能。

#### **用户故事 3.5: 完成整部剧的观看**
> **作为一个即将追完一部剧的用户**, 我想要**在标记完最后一集时，系统能智能地提示我将整部剧标记为“已看”**，以便**我能无缝地完成追踪，并将这部剧归档到我的“已看”列表中**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 当我将一部已知结局的剧集的最后一集标记为“已看”时，系统必须自动弹出一个确认提示。
    *   AC2: 该提示应询问：“恭喜！您已追完所有剧集，是否要将《剧集名称》标记为‘已看’？”
    *   AC3: 如果我确认，该剧集的状态将自动从“在看”切换为“已看”。
    *   AC4: 在确认后，系统必须记录下当前日期作为该剧集的“完成日期”，以供统计功能使用。
    *   AC5: 如果我取消提示，剧集状态将保持为“在看”。
    *   AC6: 状态变更为“已看”后，必须在应用所有地方实时反映。
*   **故事点:** 2
*   **优先级:** Must-have
*   **依赖关系:** 故事3.2

#### **用户故事 3.3: 高效地批量标记进度**
> **作为一个需要快速更新进度的用户**, 我想要**使用批量操作，一次性将多集、甚至整季标记为“已看”**，以便**我能高效地同步我的观看历史，而无需逐集点击**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在进度追踪页的每一季列表顶部，必须提供一个“标记本季为已看”的功能按钮。
    *   AC2: 我可以通过“长按”某一集的复选框，来触发一个“标记至此”的操作。
    *   AC3: 批量标记操作后，个人影视库列表页的进度条和进度文本，必须同步更新。
*   **故事点:** 3
*   **优先级:** Should-have
*   **依赖关系:** 故事3.2

#### **用户故事 3.4: 搁置或重置观看状态**
> **作为一个用户**, 我想要**能将一部“在看”的剧集重新标记回“想看”**，或者**清除其所有的观看记录**，以便**我能搁置一部暂时不想看的剧，或为二刷做准备**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 对于“在看”的剧集，我能找到“设为想看”（搁置）的选项。
    *   AC2: 执行“设为想看”后，该剧集会移回“想看”列表，但其已有的观看进度记录必须被保留。
    *   AC3: 在进度追踪页面，我能找到一个“重置观看记录”的选项。
    *   AC4: 执行“重置”操作（需二次确认）后，该剧集的所有单集都会被标记为未看，但其状态仍保持为“在看”。
*   **故事点:** 2
*   **优先级:** Should-have
*   **依赖关系:** 故事3.1, 3.2

#### **用户故事 3.6: 为误操作提供撤销选项**
> **作为一个用户**, 我想要**在执行了批量标记操作后，能有一个短暂的撤销机会**, 以便**我能轻松地纠正我的误操作**。
*   **故事点:** 2
*   **优先级:** Could-have
*   **依赖关系:** 故事3.3

---
好的，遵照您的指示，我将继续输出剩余的Epics，保持与之前完全一致的详尽格式。

---

### **Epic 4: 观影规划与提醒 (Viewing Planning & Alerts)**

#### **用户故事 4.3 (修订版): 以日历视图查看未来播出与过往记录**
> **作为一个用户**, 我想要**在一个日历视图中，不仅能看到未来的播出安排，还能回顾我过去的每日观看记录**，以便**这个日历能成为我的、完整的个人观影日记**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 应用的主导航中有一个“日历”标签页。
    *   AC2: “日历”页面的默认视图是一个按日期顺序排列的“议程列表”。
    *   AC3: 应用需提供一个切换按钮，允许我将视图切换为传统的“月视图”。
    *   AC4: 日历上会默认展示我所有“在看”和“想看”（特指即将开播）的剧集。
    *   AC5: 日历中的每个条目，需包含剧集海报、名称、季集号和播出平台信息。
    *   AC6: 当我点击日历中的任何一个剧集条目时，应用必须直接跳转到该剧集的进度追踪页面，并自动定位到对应的那一集。
    *   AC7: 当我向上滚动浏览过去的日期时，日历必须能展示我在那天实际“标记为已看”的剧集记录，并有独特的视觉标识。
    *   AC8: 当我将一部剧的状态从“在看”改为“搁置”或将其从片库移除时，它所有未来的播出条目必须立即从日历中消失。
    *   AC9: 如果我的日历在某个时间范围内没有任何内容，页面必须显示一个友好的空状态插图和引导文字。
*   **故事点:** 8
*   **优先级:** Should-have
*   **依赖关系:** 故事3.2, 2.3

#### **用户故事 4.1 (强化版): 接收新集播出提醒**
> **作为一个正在追剧的用户**, 我想要**在我追踪的剧集有新一集播出时，及时收到一个推送通知**，以便**我能第一时间知道更新，而不会错过任何剧情**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 对于我设置为“在看”状态的剧集，当其有新一集内容播出时，我的设备应能收到一条推送通知。
    *   AC2: 推送通知的发送时间，必须基于该剧集在其首播地当地时区的播出时间。后台系统必须能正确处理时区换算。
    *   AC3: 推送通知的文案必须清晰、简洁，格式为：“《[剧集名]》 [季集号] 现已播出。”
    *   AC4: 点击该推送通知，必须能直接打开IMList应用，并跳转到该剧集的进度追踪页面，同时智能焦点应定位到刚刚播出的那一集。
    *   AC5: 后台系统必须有去重逻辑，确保不会因为数据源的临时波动，而对同一集发送重复的通知。
    *   AC6: 如果我尚未授予应用“通知权限”，在我首次尝试开启任何提醒设置时，应用必须先友好地向我解释为何需要此权限，然后再触发系统的权限请求弹窗。
*   **故事点:** 8
*   **优先级:** Should-have
*   **依赖关系:** 故事3.1, Epic 8

#### **用户故事 4.2: 管理提醒设置**
> **作为一个用户**, 我想要**能自由地控制哪些剧集要提醒我，哪些不要**，以便**我只收到我真正关心的通知，避免不必要的打扰**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在应用的全局“设置”页面，我能找到一个总开关，可以一键启用或禁用所有的新集播出提醒。
    *   AC2: 在每一部剧集的详情页面，我能找到一个独立的提醒开关（例如一个小铃铛图标），它的设置可以覆盖全局设置。
    *   AC3: 我可以只为某一部“在看”的剧集关闭提醒，而不影响其他剧集。
    *   AC4: 对于“想看”列表中的剧集，我也能在这个详情页的开关上，手动开启“开播提醒”。
*   **故事点:** 3
*   **优先级:** Should-have
*   **依赖关系:** 故事4.1

#### **用户故事 4.4: 离线访问日历**
> **作为一个用户**, 我想要**在没有网络连接的情况下，依然能够打开和浏览我的播出日历**，以便**我能随时随地查看我的观影计划**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 应用必须在本地缓存未来（例如30天）和过去（例如90天）的日历数据。
    *   AC2: 当设备处于离线状态时，打开日历页面必须能立即从缓存中加载并展示数据。
    *   AC3: 在离线模式下，必须在界面上提供一个清晰的视觉提示。
*   **故事点:** 3
*   **优先级:** Could-have
*   **依赖关系:** 故事4.3

#### **用户故事 4.5: 在日历中追溯提醒状态**
> **作为一个依赖提醒功能的用户**, 我想要**在播出日历中，清晰地看到系统是否已经为某天的某个剧集发送了播出提醒**，以便**我能确认提醒系统的运行状态，并能方便地回顾收到了哪些更新通知**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在“播出日历”视图中，对于系统已经成功发送了播出提醒的剧集条目，必须有一个明确的视觉标记。
    *   AC2: 这个标记可以是一个被点亮的小铃铛图标，或一个“已通知”的标签。
    *   AC3: 这个状态标记必须与后台的推送任务紧密关联。
    *   AC4: （可选）点击该标记时，可以显示提醒发送的具体时间。
*   **故事点:** 3
*   **优先级:** Could-have
*   **依赖关系:** 故事4.1, 4.3

---

### **Epic 5: 个性化整理与回顾 (Personal Curation & Review)**

#### **用户故事 5.1 (强化版): 创建和管理自定义列表**
> **作为一个喜欢整理的用户**, 我想要**创建带有自定义名称和描述的个人列表**，以便**我能根据自己的逻辑（如“我的科幻神作”、“假期片单”）来组织我感兴趣的剧集**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 应用的主导航中有一个“我的列表”的入口。
    *   AC2: 在“我的列表”页面，我能看到一个“创建新列表”的按钮。
    *   AC3: 创建列表时，我需要输入列表名称（必填）和列表描述（选填）。
    *   AC4: 我可以对已创建的列表进行重命名、编辑描述和删除（需二次确认）操作。
    *   AC5: 当我首次进入“我的列表”页面且没有任何列表时，页面必须显示一个友好的空状态插图和引导文字。
*   **故事点:** 3
*   **优先级:** Should-have
*   **依赖关系:** 故事1.2

#### **用户故事 5.2: 向自定义列表添加和移除剧集**
> **作为一个正在整理片单的用户**, 我想要**能方便地将剧集添加到一个或多个自定义列表中，并在需要时将其移除**，以便**我能灵活地维护我创建的这些收藏集**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在剧集的详情页面，我能找到一个“添加到列表”的按钮。
    *   AC2: 点击后，会弹出一个包含我所有自定义列表的菜单，我可以勾选目标列表。
    *   AC3: 在我的主影视库中，我可以通过“批量选择”模式，将多个剧集一次性添加到某个自定义列表中。
    *   AC4: 在自定义列表的内部，我可以通过操作，将某个剧集从当前列表中移除。
    *   AC5: 当一部剧集被从整个片库中彻底移除时，它也必须从所有包含它的自定义列表中被自动清除。
*   **故事点:** 5
*   **优先级:** Should-have
*   **依赖关系:** 故事5.1, 故事2.7

#### **用户故事 5.3 (强化版): 策展与展示自定义列表**
> **作为一个列表创建者**, 我想要**能手动调整列表内剧集的顺序，并能看到它们完整的动态信息**，以便**我能按我的意愿策展一个有序、信息丰富的个性化片单**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 当我打开一个自定义列表时，我可以通过长按并拖拽的方式，自由地调整列表内剧集条目的顺序。
    *   AC2: 在列表的菜单中，我也能找到按“剧集名”、“年份”等自动排序的选项。
    *   AC3: 列表内的每个剧集卡片，其设计和信息展示逻辑，必须与主影视库的卡片完全一致。
    *   AC4: 当我打开一个没有任何剧集的自定义列表时，页面必须显示一个友好的空状态插图和引导文字。
*   **故事点:** 5
*   **优先级:** Should-have
*   **依赖关系:** 故事5.2

#### **用户故事 5.6 (强化版): 查看个人整体观看统计**
> **作为一个长期使用IMList的用户**, 我想要**查看一个包含我所有观看历史的可视化数据仪表盘**，以便**我能直观地了解我的观影习惯，并从中获得成就感**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在应用中有一个清晰的“统计”入口。
    *   AC2: 仪表盘顶部必须以醒目的“英雄数据”形式，展示我的核心指标：总观看时长、总观看集数、已看完的剧集总数。
    *   AC3: 仪表盘需通过清晰的条形图，展示我的数据分布，至少包含：类型分布、播出平台分布。
    *   AC4: 仪表盘需提供趣味性的“Top 5”榜单，例如：我观看时长最长的剧集Top 5。
    *   AC5: 当我首次进入统计页面且没有任何“已看”记录时，页面必须显示一个友好的空状态。
*   **故事点:** 8
*   **优先级:** Could-have
*   **依赖关系:** Epic 3

#### **用户故事 5.8: 管理与校准我的统计数据**
> **作为一个对数据准确性有高要求的用户**, 我想要**能将我的历史导入记录从日常统计中排除，并能手动修改剧集的完成日期**，以便**我能确保我的观影报告精确地反映我真实的观看行为**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 当我短时间内批量标记大量剧集为“已看”时，系统应能识别并提示我是否将这些记录标记为“历史导入”。
    *   AC2: 在应用的设置或统计页面，我能找到一个管理选项，可以查看并管理被标记为“历史导入”的剧集。
    *   AC3: 所有被标记为“历史导入”的剧集，其观看时长和集数不应被计入按具体时间范围筛选的统计报告中。
    *   AC4: 我必须能在剧集的详情页手动修改它的“完成日期”。
*   **故事点:** 5
*   **优先级:** Could-have
*   **依赖关系:** 故事5.6, 5.7

#### **用户故事 5.4 (强化版): 分享自定义列表为精美图片**
> **作为一个为自己创建的片单感到自豪的用户**, 我想要**能将我的自定义列表一键生成为一张设计精美的图片**，以便**我能方便地在社交媒体上与朋友分享我的品味和推荐**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在每个自定义列表的页面，我能找到一个清晰的“分享”按钮。
    *   AC2: 点击分享后，会弹出一个预览和自定义界面，允许我选择不同的布局模板和配置要包含的内容。
    *   AC3: 系统生成的最终图片，必须包含列表的标题、剧集信息、清晰的IMList品牌Logo，以及一个指向我们应用商店页面的二维码。
    *   AC4: 图片生成后，会自动调用系统的原生分享窗口。
*   **故事点:** 5
*   **优先级:** Could-have
*   **依赖关系:** 故事5.3

#### **用户故事 5.7 (强化版): 查看并分享我的年度观影报告**
> **作为一个喜欢回顾和分享的用户**, 我想要**能筛选查看我特定年份的观影报告，并能将其生成为一张精美的总结图片**，以便**我能回顾一整年的观影历程**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在统计仪表盘页面，我能找到一个时间范围筛选器，允许我在“全部时间”和具体的“年份”之间切换。
    *   AC2: 当我选择一个年份后，页面上的所有数据指标都会动态刷新，只显示该年度的数据。
    *   AC3: 在任何“年度报告”视图下，页面上都会出现一个醒目的“分享我的年度报告”按钮。
    *   AC4: 点击该按钮，会调用“生成式图片分享”功能，并使用一个专门为“年度报告”设计的独特信息图模板。
    *   AC5: 生成的分享图片上，必须包含IMList的品牌标识和应用商店二维码。
    *   AC6: “年度报告”的数据统计范围，被明确定义为：用户在该公历年度内，所有标记为“已看”的单集记录的总和。
    *   AC7: 如果某年度的数据量过少，在生成分享图片时，系统应自动采用一个更紧凑、更简洁的模板。
*   **故事点:** 5
*   **优先级:** Could-have
*   **依赖关系:** 故事5.6, 5.4

#### **用户故事 5.5: 查看自定义列表的专属统计**
> **作为一个精心创建了一个列表的用户**, 我想要**能看到只针对这个列表内所有影片的专属统计数据**，以便**我能从我自己的策展中，发现有趣的模式和洞察**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在每个自定义列表的页面顶部或菜单中，我能找到一个“查看统计”的入口。
    *   AC2: 点击后，会展示一个只针对当前列表内所有条目的统计仪表盘。
    *   AC3: 该仪表盘至少应包含以下核心指标：完成度、总观看时长、平均评分、类型/年代分布。
    *   AC4: 在分享自定义列表时，我可以选择是否将这些统计数据包含在分享图片中。
*   **故事点:** 3
*   **优先级:** Could-have
*   **依赖关系:** 故事5.3, 5.4, 5.6

#### **用户故事 5.9: 在个人影视库中获得智能洞察与快捷整理**
> **作为一个正在浏览我个人影视库的用户**, 我想要**系统能在我的列表顶部或内容之间，智能地展示一些有趣的统计洞察卡片或快捷整理的建议**，以便**我能在日常使用中就能发现数据的乐趣**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在“已看”列表的顶部，可以周期性地、非永久地展示一些可划掉的洞察卡片。
    *   AC2: 在用户的影视库列表中，系统可以智能地识别出一些可供整理的集合，并提供“一键创建列表”的建议卡片。
    *   AC3: 这些智能卡片本身，也应作为进入完整“统计”页面或“自定义列表”页面的快捷入口。
*   **故事点:** 8
*   **优先级:** Won't-have (for v1.0)
*   **依赖关系:** Epic 5所有其他故事

---
好的，遵照您的指示，我将继续输出最后一个功能性Epic，保持与之前完全一致的详尽格式。

---

### **Epic 6: 离线与同步 (Offline & Sync)**

*(注：此Epic的故事已在第一部分与Epic 8合并输出，因为它们是项目的技术基础，在此不再重复)*

---

### **Epic 7: 商业化 (Monetization)**

#### **用户故事 7.1 (强化版): 在应用中集成并展示广告**
> **作为一个开发团队**, 我想要**在应用中集成一个可扩展的广告中介平台，并根据明确的规则，在适当时机和位置展示特定类型的广告**，以便**在为产品创造收入的同时，最大限度地降低对核心用户体验的干扰**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 应用必须集成Google AdMob SDK作为主要的广告SDK和中介平台。
    *   AC2: 在AdMob中介下，需准备好集成Meta, AppLovin, Unity Ads等至少三种主流的第三方广告网络SDK。
    *   AC3: **原生广告规则:**
        *   **位置:** 只能被放置在信息流列表（发现页、主影视库、自定义列表、搜索结果页）中。
        *   **频次:** 在列表中，每15个内容条目最多展示1个广告位。
    *   AC4: **横幅广告规则:**
        *   **位置:** 只能被固定放置在“发现”页和“自定义列表”管理页的底部。
        *   **禁止:** 严禁在用户的核心操作页面（如影视库主页、进度追踪页、日历页）放置。
    *   AC5: **插屏广告规则:**
        *   **触发时机:** 只能在非核心路径的、自然的流程中断处（如返回页面时）被触发。
        *   **频率控制:** 同一用户在5分钟内最多只能看到一次。
        *   **禁止:** 严禁在应用启动时、执行核心的正向操作时、或在列表滚动过程中弹出。
    *   AC6: 所有广告的加载过程都不能阻塞UI线程或影响列表的滚动性能。
*   **故事点:** 8
*   **优先级:** Should-have
*   **依赖关系:** Epic 8

#### **用户故事 7.2 (强化版): 购买高级版以移除广告**
> **作为一个不希望被广告打扰的用户**, 我想要**能方便地通过付费（订阅或买断）升级到“IMList高级版”**，以便**我能享受一个完全无广告的、更纯粹的应用体验**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 应用内必须有一个清晰的“升级到高级版”的入口。
    *   AC2: 升级页面需要清晰地展示高级版的权益以及不同的付费选项。页面展示的价格必须是通过Google Play Billing Library获取的、本地化的价格。
    *   AC3: 必须集成Google Play Billing Library，并能正确地拉起谷歌官方的支付流程。
    *   AC4: 在支付过程中，如果发生网络错误、支付失败或用户手动取消，应用不能崩溃，并应给出友好、清晰的提示。
    *   AC5: 当我成功完成支付后，应用内的所有广告必须立即、永久地消失。
    *   AC6: 购买成功后，在“设置”页面的账户信息中，必须有明确的“IMList高级版”或类似的徽章/标识。
    *   AC7: 我的“高级版”状态必须与我的IMList账户绑定。
*   **故事点:** 8
*   **优先级:** Should-have
*   **依赖关系:** 故事1.2, 7.1

#### **用户故事 7.3: 恢复与管理我的购买状态**
> **作为一个已经付费的用户**, 我想要**在重装应用或更换设备后，能轻松地恢复我已购买的高级版状态**，并且**能方便地管理我的订阅**，以便**我能持续地享受我应有的权益**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 在应用的“升级”页面或“设置”页面，必须提供一个清晰的“恢复购买”按钮。
    *   AC2: 点击“恢复购买”后，应用会通过Google Play Billing Library检查与当前Google账户关联的有效购买记录，并自动为我解锁高级版权益。
    *   AC3: 在用户每次启动应用时，应用应在后台静默地检查一次购买状态，确保高级版状态能被自动恢复。
    *   AC4: 在应用的账户管理或设置页面，必须提供一个“管理订阅”的链接，点击后能直接跳转到Google Play商店的订阅管理中心。
*   **故事点:** 3
*   **优先级:** Should-have
*   **依赖关系:** 故事7.2

#### **用户故事 7.4: 在特定场景下引导用户升级**
> **作为一个深度使用免费版的用户**, 我想要**在体验到某些高级功能的价值或达到某个使用里程碑时，应用能自然地向我展示升级到高级版的好处**，以便**我能在最需要的时候，了解到付费能为我带来怎样的体验升级**。
*   **验收标准 (Acceptance Criteria):**
    *   AC1: 我们可以将某些非核心但价值很高的功能，设置为“高级版预览”模式（例如，免费用户最多只能创建3个自定义列表）。当用户达到限制时，系统会提示并引导升级。
    *   AC2: 当用户达成某个深度使用行为（例如，累计追踪超过50部剧集）时，可以触发一次性的升级引导。
    *   AC3: 所有升级引导都必须是情景化、非阻塞的，严禁使用插屏广告来推销自己。
    *   AC4: 该引导系统的实现，应预留进行A/B测试的接口，以便未来可以测试和优化转化策略。
*   **故事点:** 5
*   **优先级:** Could-have
*   **依赖关系:** 故事7.2, Epic 5等

---
**[文档结束]**