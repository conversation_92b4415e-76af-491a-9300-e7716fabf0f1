
**版本: 1.2 (中文描述最终版)**
**日期: 2025-08-22**
**规范标准: OpenAPI 3.0.3**

```yaml
openapi: 3.0.3
info:
  title: IMList 后台API
  description: |
    IMList应用的API规范, 由Firebase Functions提供后台能力。
    本API旨在作为一个安全高效的后台层，处理不适合由客户端直接与Firebase交互的任务。
    其核心职责包括:
    1.  **保护第三方API密钥** (例如 TMDB)。
    2.  **实现服务端缓存** 以提升性能和降低外部依赖。
    3.  **执行安全的业务逻辑** (例如 购买验证)。
    4.  **运行定时和内部系统任务**。
  version: 1.2.0
servers:
  - url: https://{region}-{project-id}.cloudfunctions.net/api/v1
    description: Firebase Functions 生产环境URL (版本 1)
    variables:
      region:
        default: us-central1
      project-id:
        default: imlist-prod

tags:
  - name: Discovery (发现)
    description: 用于聚合内容发现的端点。
  - name: Shows (影视信息)
    description: 用于获取特定影视详情和搜索的端点。
  - name: User (用户)
    description: 与用户账户管理相关的端点。
  - name: Purchases (购买)
    description: 用于处理应用内购买安全验证的端点。
  - name: Tasks (内部任务)
    description: 用于触发后台任务的内部端点 (仅限管理员/系统调用)。

paths:
  /discovery:
    get:
      summary: 获取“发现”页的全部内容
      description: |
        **业务逻辑:** 提供一个单一的、高性能的端点，以完全渲染应用的“发现”页面。
        1.  它会并行地从TMDB API获取多个精选列表 (例如 "时下流行", "高分推荐")。
        2.  如果请求是经过身份验证的 (包含了有效的Bearer Token), 它还会从Firestore中获取该用户片库中所有影视的TMDB ID列表。
        3.  最后，它会将一个布尔值`inLibrary`标志注入到每个影视条目中，该标志表明此条目是否已在用户片库内。这使得客户端无需任何额外的数据库查询就能正确渲染UI状态，从而解决了N+1查询问题。
      tags:
        - Discovery (发现)
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 成功响应，包含“发现”页的全部内容。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DiscoveryPage'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /shows/{tmdbId}:
    get:
      summary: 获取聚合的影视详情
      description: |
        **业务逻辑:** 作为TMDB影视详情接口的一个安全且带缓存的代理。
        1.  **安全:** 保护TMDB API密钥不被暴露在客户端。
        2.  **聚合:** 它会调用TMDB的多个端点 (例如 `/details`, `/credits`, `/watch/providers`) 并将数据聚合成一个单一、干净的`ShowDetails`对象返回给客户端。
        3.  **缓存:** 在调用TMDB之前，它会首先检查Firestore的`shows_metadata`集合中是否存在该数据的近期缓存副本。如果存在且未过期，将立即返回缓存数据以提升性能并减少TMDB API的使用。如果不存在，则从TMDB获取，更新缓存，然后再返回数据。
      tags:
        - Shows (影视信息)
      security:
        - bearerAuth: []
      parameters:
        - name: tmdbId
          in: path
          required: true
          description: 影视剧集在The Movie Database上的ID。
          schema:
            type: integer
      responses:
        '200':
          description: 成功响应，包含聚合后的影视详情。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShowDetails'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'

  /search/shows:
    get:
      summary: 搜索影视剧集
      description: |
        **业务逻辑:** 作为TMDB搜索API的一个简单、安全的代理。其主要目的是保护TMDB API密钥。未来的增强功能可能包括对频繁搜索的词条进行服务端缓存。
      tags:
        - Shows (影视信息)
      security:
        - bearerAuth: []
      parameters:
        - name: query
          in: query
          required: true
          description: 搜索的关键词字符串。
          schema:
            type: string
        - name: page
          in: query
          description: 结果的页码。
          schema:
            type: integer
            default: 1
      responses:
        '200':
          description: 成功响应，包含分页的搜索结果列表。
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShowSearchResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /user/me:
    delete:
      summary: 删除用户账户及其所有关联数据
      description: |
        **业务逻辑:** 实现用户的“被遗忘权”，是应用商店合规性的关键要求。
        1.  通过用户认证的UID来识别用户。
        2.  在Firestore中执行跨多个集合的删除操作，删除与该`userId`关联的所有文档 (`users`, `user_libraries`, `user_progress`, `user_lists`, `user_list_items`)。
        3.  从Firebase Storage中删除任何关联的用户数据 (例如 备份文件)。
        4.  最后，调用Firebase Authentication API删除用户自身的认证记录。
        整个过程是不可逆的。
      tags:
        - User (用户)
      security:
        - bearerAuth: []
      responses:
        '204':
          description: 无内容 - 用户数据已成功删除。
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
  
  /purchases/verify:
    post:
      summary: 验证一笔Google Play购买
      description: |
        **业务逻辑:** 安全购买流程的核心，旨在防止客户端欺诈。
        1.  从刚刚通过Google Play Billing库完成购买的客户端接收一个`purchaseToken`。
        2.  执行一次服务器到服务器的调用，请求Google Play Developer API来验证该票据的真实性。
        3.  如果验证通过，则更新用户在Firestore中的文档 (`isPremium: true`)。
        4.  然后，向Google确认/消耗此笔购买以防止重放攻击。
        客户端不应直接依赖此API的响应来解锁功能，而应通过监听Firestore文档的实时更新来触发。
      tags:
        - Purchases (购买)
      security:
        - bearerAuth: []
      requestBody:
        $ref: '#/components/requestBodies/VerifyPurchaseBody'
      responses:
        '200':
          description: 购买验证请求已成功进入队列。
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "VERIFICATION_QUEUED"
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'

  /tasks/check-episode-updates:
    post:
      summary: (内部接口) 触发剧集更新检查
      description: |
        **业务逻辑:** 新集播出通知功能的核心引擎。
        1.  通常由Cloud Scheduler定时触发，但此端点允许手动或调试触发。
        2.  它会扫描数据库中所有被活跃追踪的剧集，查询TMDB以获取更新，识别新播出的剧集，找到所有为这些剧集开启了提醒的相关用户，然后通过FCM将推送通知加入队列。
        这是一个长时间运行的异步任务。
      tags:
        - Tasks (内部任务)
      security:
        - adminAuth: []
      responses:
        '202':
          description: 已接受 - 任务已成功进入队列。
        '401':
          description: 未授权。
        '403':
          description: 禁止访问 - 调用者没有管理员权限。

  /tasks/refresh-metadata:
    post:
      summary: (内部接口) 触发元数据刷新任务
      description: |
        **业务逻辑:** 一个数据质量和维护任务。
        1.  它会扫描`user_libraries`集合，查找那些在很长时间内（例如超过30天）没有刷新过的条目。
        2.  对于这些条目，它会从我们的`shows_metadata`缓存（或TMDB）获取最新的元数据（标题、海报），并更新这些冗余字段。这确保了用户的片库不会因为源数据的变更而显得陈旧。
      tags:
        - Tasks (内部任务)
      security:
        - adminAuth: []
      responses:
        '202':
          description: 已接受 - 刷新任务已进入队列。

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: 一个从客户端SDK获取的Firebase Authentication ID Token，通过 'Authorization: Bearer <token>' 请求头发送。
    adminAuth:
      type: apiKey
      in: header
      name: X-Internal-API-Key
      description: 用于触发内部、仅限管理员任务的密钥，通过Firebase Functions的环境变量配置。

  requestBodies:
    VerifyPurchaseBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              purchaseToken:
                type: string
                description: 从Google Play Billing库接收到的购买票据。
              productId:
                type: string
                description: 正在购买的商品ID (例如 'premium_monthly', 'premium_lifetime')。

  responses:
    UnauthorizedError:
      description: 未授权。提供的ID Token缺失、无效或已过期。
    BadRequestError:
      description: 错误的请求。一个必需的参数缺失或无效。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    NotFoundError:
      description: 未找到。请求的资源不存在。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    InternalServerError:
      description: 服务器内部错误。服务器上发生了未预期的错误。
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: "一个简短的、机器可读的错误码 (例如 'INVALID_TOKEN')。"
        message:
          type: string
          description: "一段人类可读的、用于向用户展示的错误信息。"

    ImagePaths:
      type: object
      description: "包含图片路径的可复用数据模型。"
      properties:
        posterPath:
          type: string
          description: "海报图片的部分路径。客户端需要自行拼接TMDB的图片基础URL。"
          example: "/q6y0Go1tsGEsmtFryDOJo3dEmqu.jpg"
        backdropPath:
          type: string
          description: "背景图片的部分路径。客户端需要自行拼接TMDB的图片基础URL。"
          example: "/8Y43POKjjKDGI9mh89NW0NAzzp8.jpg"

    Show:
      type: object
      description: "代表一个影视剧集的基础信息模型。"
      properties:
        tmdbId:
          type: integer
          description: "该剧集在TMDB上的唯一ID。"
          example: 1399
        title:
          type: string
          description: "剧集的本地化标题。"
          example: "权力的游戏"
        releaseDate:
          type: string
          format: date
          description: "首播日期。"
          example: "2011-04-17"
      allOf:
        - $ref: '#/components/schemas/ImagePaths'

    ShowInList:
      type: object
      description: "用于列表（如发现页）中展示的影视条目，比基础模型增加了个人状态。"
      properties:
        inLibrary:
          type: boolean
          description: "表示该剧集是否已在当前用户的片库中。此字段仅在认证用户请求时存在。"
          example: true
      allOf:
        - $ref: '#/components/schemas/Show'

    ShowDetails:
      type: object
      description: "代表一个影视剧集的详尽信息模型，用于详情页。"
      properties:
        overview:
          type: string
          description: "剧情简介。"
        genres:
          type: array
          description: "类型列表。"
          items:
            type: string
            example: "科幻 & 奇幻"
        cast:
          type: array
          description: "主要演职员列表。"
          items:
            type: object
            properties:
              name:
                type: string
                description: "演员姓名。"
                example: "Emilia Clarke"
              character:
                type: string
                description: "角色名称。"
                example: "Daenerys Targaryen"
              profilePath:
                type: string
                description: "演员照片的部分路径。"
                example: "/86jeYFV40Kyb5h4hXDP3CHVb6Ky.jpg"
        watchProviders:
          type: object
          description: "根据用户所在地区，在哪些流媒体平台可用的信息结构（直接来源于TMDB）。"
      allOf:
        - $ref: '#/components/schemas/Show'

    ShowSearchResult:
      type: object
      description: "搜索API返回的分页结果。"
      properties:
        page:
          type: integer
          description: "当前页码。"
        results:
          type: array
          items:
            $ref: '#/components/schemas/Show'
        totalPages:
          type: integer
          description: "总页数。"
        totalResults:
          type: integer
          description: "总结果数。"

    Carousel:
      type: object
      description: "代表发现页中的一个“泳道”或“主题横向滚动列表”。"
      properties:
        title:
          type: string
          description: "该泳道的主题，例如“时下流行”。"
          example: "时下流行"
        items:
          type: array
          items:
            $ref: '#/components/schemas/ShowInList'

    DiscoveryPage:
      type: object
      description: "发现页API返回的顶层数据结构。"
      properties:
        carousels:
          type: array
          items:
            $ref: '#/components/schemas/Carousel'
```