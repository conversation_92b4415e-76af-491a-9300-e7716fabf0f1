
我将为您输出**产出物 4: 《数据库模式》**的最终、完整版本。这份文档是我们对数据进行多次评审和优化的最终成果，体现了我们对性能、可扩展性和数据一致性的高度重视。

这份模式定义将作为前后端开发的数据契约，确保所有组件对数据的理解和操作保持一致。

---


**版本: 2.1 (最终版)**
**日期: 2025-08-22**
**数据库系统: Firestore (NoSQL Document Database)**

#### **设计哲学**
本数据库模式基于Firestore，采用**“根集合” (Root Collections)**模型以实现最大的可扩展性和简化批量操作（如用户删除）。为优化核心读取路径的性能，本模式在关键位置采用了**“数据冗余” (Denormalization)**策略。为保证数据写入的健壮性，关键集合采用了**“复合主键” (Composite Key)**作为文档ID，以实现操作的幂等性。

---
```sql
-- Firestore Database Schema for IMList
-- Version: 2.1 (Finalized with Composite Keys and List Schema)
-- NOTE: This SQL DDL is a descriptive representation. The actual implementation is in Firestore.

-- -----------------------------------------------------
-- Collection: users
-- Description: Stores core user profile and status.
-- Document ID: Firebase Authentication UID (string)
-- -----------------------------------------------------
CREATE TABLE users (
    -- Document ID is the Firebase Auth UID
    email           STRING      NOT NULL,  -- User's email from auth provider
    displayName     STRING      NULL,      -- User's display name from auth provider
    photoURL        STRING      NULL,      -- URL for user's profile picture from auth provider
    createdAt       TIMESTAMP   NOT NULL,  -- Timestamp of account creation
    isPremium       BOOLEAN     NOT NULL DEFAULT FALSE, -- Flag for premium status, updated by backend function
    lastBackupAt    TIMESTAMP   NULL       -- Timestamp of the last successful manual backup
);
-- Recommended Index: N/A (Firestore handles single-field queries automatically)


-- -----------------------------------------------------
-- Collection: user_libraries
-- Description: Stores all shows a user has added to their library.
-- Document ID: Auto-generated by Firestore
-- -----------------------------------------------------
CREATE TABLE user_libraries (
    -- Document ID is auto-generated
    userId          STRING      NOT NULL,  -- FK to users collection (Auth UID)
    tmdbId          INTEGER     NOT NULL,  -- The Movie Database ID for the show
    title           STRING      NOT NULL,  -- Denormalized for high-performance list display
    posterPath      STRING      NULL,      -- Denormalized for high-performance list display
    status          STRING      NOT NULL,  -- Enum: "wanna_see", "watching", "watched"
    addedAt         TIMESTAMP   NOT NULL,  -- Timestamp when the show was added
    completedAt     TIMESTAMP   NULL,      -- Timestamp when the show was marked as 'watched'
    
    -- Denormalized fields for high-performance reads.
    -- These fields avoid expensive aggregation queries on the client.
    totalEpisodes   INTEGER     NOT NULL DEFAULT 0, -- Total known episodes for the show (from TMDB)
    watchedEpisodesCount INTEGER NOT NULL DEFAULT 0  -- Count of watched episodes
);
-- CRITICAL NOTE: Any write to 'user_progress' that affects the count MUST be in a batched write
--                that atomically updates 'watchedEpisodesCount' in this collection.
-- Recommended Indexes:
-- 1. (userId, status, addedAt) - For fetching a user's specific list (e.g., 'watching' list) sorted by date.
-- 2. (userId, tmdbId) - To quickly check if a user has already added a specific show.


-- -----------------------------------------------------
-- Collection: user_progress
-- Description: Stores individual episode watch events (Lightweight Event Sourcing).
-- Document ID: A composite key: "{userId}_{tmdbId}_{seasonNumber}_{episodeNumber}_{instanceId}"
-- -----------------------------------------------------
CREATE TABLE user_progress (
    -- Document ID is a composite key for idempotency (prevents duplicate watch events).
    userId          STRING      NOT NULL, -- Denormalized for security rules and simple queries
    tmdbId          INTEGER     NOT NULL, -- FK to the show
    seasonNumber    INTEGER     NOT NULL, -- Season number of the episode
    episodeNumber   INTEGER     NOT NULL, -- Episode number
    watchedAt       TIMESTAMP   NOT NULL, -- The actual timestamp of the watch event, crucial for statistics
    instanceId      STRING      NOT NULL DEFAULT '1' -- Watch instance (e.g., '1' for first watch, '2' for rewatch)
);
-- Recommended Indexes:
-- 1. (userId, tmdbId) - To fetch all progress for a specific show for a user.
-- 2. (userId, watchedAt) - For generating annual/monthly statistics efficiently.


-- -----------------------------------------------------
-- Collection: user_lists
-- Description: Stores metadata for user-created custom lists.
-- Document ID: Auto-generated by Firestore
-- -----------------------------------------------------
CREATE TABLE user_lists (
    -- Document ID is auto-generated
    userId          STRING      NOT NULL, -- FK to users collection
    name            STRING      NOT NULL, -- Name of the list
    description     STRING      NULL,     -- Optional description for the list
    createdAt       TIMESTAMP   NOT NULL,
    itemCount       INTEGER     NOT NULL DEFAULT 0 -- Denormalized count for display efficiency
);
-- Recommended Index: (userId, createdAt) - To fetch all lists for a user, sorted by creation date.


-- -----------------------------------------------------
-- Collection: user_list_items
-- Description: Maps shows to custom lists (Many-to-Many relationship).
-- Document ID: A composite key: "{listId}_{tmdbId}"
-- -----------------------------------------------------
CREATE TABLE user_list_items (
    -- Document ID is a composite key for idempotency.
    listId          STRING      NOT NULL, -- FK to user_lists collection
    userId          STRING      NOT NULL, -- Denormalized for security rules and cross-list queries
    tmdbId          INTEGER     NOT NULL,
    title           STRING      NOT NULL, -- Denormalized for display
    posterPath      STRING      NULL,     -- Denormalized for display
    addedAt         TIMESTAMP   NOT NULL,
    -- 'rank' field is essential for the manual drag-and-drop sorting feature.
    -- It can be an integer or a string for lexical ordering to handle insertions.
    rank            STRING      NOT NULL
);
-- Recommended Indexes:
-- 1. (listId, rank) - To fetch items for a list in their custom user-defined order.
-- 2. (userId, tmdbId) - To find all lists a show belongs to for a specific user.
```

---
这份详尽的数据库模式，为我们的后端数据存储提供了坚实、高效且可扩展的蓝图。