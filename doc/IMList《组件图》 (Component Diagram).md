

我将为您输出**产出物 1: 《组件图》**的最终、完整版本。这份图表不仅包含了我们确定的所有组件，还通过详细的注释和带有编号的数据流标签，清晰地呈现了它们之间的静态关系和核心交互路径。

这份组件图是我们宏观架构的“一张图胜过千言万语”的总结，可以直接放入您的技术文档库中。

---


**版本: 2.0 (最终版)**
**日期: 2025-08-22**

#### **Mermaid.js 代码**

```mermaid
graph TD
    %% Define Subgraphs (System Boundaries)
    subgraph "用户设备 (Android)"
        A[IMList Android App]
    end

    subgraph "Firebase (Google Cloud Platform Backend)"
        B[Firebase Authentication]
        C[Firestore Database]
        D[Firebase Storage]
        E[Firebase Functions-TypeScript]
        F[Firebase Cloud Messaging]
        J[Firebase Observability Suite]
    end

    subgraph "第三方服务 (Third-Party Services)"
        G[TMDB API]
        H[Google Play Store]
        I[AdMob & Mediation SDKs]
    end

    %% Define detailed components within Observability
    J --> K((Crashlytics));
    J --> L((Performance Monitoring));
    J --> M((Cloud Logging & Trace));


    %% --- Define Relationships and Data Flows ---

    %% Client to Firebase/GCP Backend Flows
    A -- "1. Google Sign-In & Get ID Token" --> B;
    A -- "2. R/W User Data (Lists, Progress, etc.)<br/>*via Firestore SDK*" --> C;
    A -- "3. Upload/Download User Backup<br/>*via Storage SDK*" --> D;
    A -- "6. Report Crashes & Performance Data" --> J;

    %% Client to Self-Hosted Backend (Firebase Functions) Flows
    A -- "10. Call Business Logic APIs<br/>*(e.g., GET /discovery, POST /verify)*" --> E;

    %% Client to Third-Party Flows
    A -- "4. In-App Purchase/Subscription<br/>*via Billing SDK*" --> H;
    A -- "5. Display Ads<br/>*via AdMob SDK*" --> I;
    
    %% Backend to Backend/Third-Party Flows
    E -- "Triggers on DB changes (e.g., onUserCreate)" --> C;
    E -- "Scheduled Job Trigger (e.g., daily check)" --> E;
    E -- "7. Fetch Show Metadata (Server-to-Server)" --> G;
    E -- "8. Trigger Push Notification" --> F;
    E -- "11. Verify Purchase Token (Server-to-Server)" --> H;
    E -- "Logs & Traces" --> M;
    
    %% Push Notification Flow
    F -- "9. Send Notification to Device" --> A;
    
    %% Security & Rules Enforcement
    B -- "Provides UID for Security Rules" --> C;
    B -- "Provides UID for Security Rules" --> D;

```

#### **图表解读与详细说明**

*   **三大板块:**
    *   **用户设备 (Android):** 代表了我们唯一的客户端应用。它是所有用户交互的起点和终点。
    *   **Firebase (Google Cloud Platform Backend):** 这是我们的核心后台，完全构建在Google托管的服务之上。它处理了数据、认证、逻辑和可观测性等所有后端职责。
    *   **第三方服务:** 代表了我们系统所依赖的、不受我们直接控制的外部服务。

*   **核心组件说明:**
    *   **A. IMList Android App:** 客户端应用，负责UI渲染、本地数据管理（RoomDB）、以及与所有后端和SDK的交互。
    *   **B. Firebase Authentication:** 提供用户身份验证服务（Google登录）。它产出的`ID Token`和`UID`是保护我们整个后台系统的安全基石。
    *   **C. Firestore Database:** 我们的主数据库，存储所有结构化的用户数据。客户端通过Firebase SDK直接与其交互，所有操作都受安全规则的保护。
    *   **D. Firebase Storage:** 用于存储非结构化数据，在我们的场景中特指用户手动备份的JSON文件。同样受安全规则保护。
    *   **E. Firebase Functions (TypeScript):** 我们自己编写的Serverless后台逻辑。它扮演着“粘合剂”和“受信任的协调者”的角色，处理客户端不应直接处理的任务。
    *   **F. Firebase Cloud Messaging (FCM):** 专业的推送通知服务，由我们的云函数触发。
    *   **J. Firebase Observability Suite:** 一个组合概念，代表了Firebase提供的整套监控和日志服务，包括：
        *   **K. Crashlytics:** 收集和分析客户端崩溃报告。
        *   **L. Performance Monitoring:** 收集和分析客户端性能指标。
        *   **M. Cloud Logging & Trace:** 集中收集所有云函数的日志，并支持分布式追踪。
    *   **G, H, I:** 分别代表提供影视数据、支付服务和广告服务的外部依赖。

*   **关键数据流解读 (带编号):**
    1.  **认证流程:** App通过Firebase Auth SDK完成用户登录。
    2.  **核心数据交互:** App直接读写Firestore来管理用户数据，这是最高频的操作。
    3.  **备份流程:** App直接与Firebase Storage交互，上传下载大文件。
    4.  **支付流程:** App通过Google Play Billing SDK与应用商店交互。
    5.  **广告流程:** App通过AdMob SDK与广告网络交互。
    6.  **可观测性数据流:** App将崩溃和性能数据上报给Firebase。
    7.  **元数据获取:** **Firebase Functions**作为我们安全的代理，去调用TMDB API。**客户端永远不会直接调用TMDB。**
    8.  **提醒触发:** **Firebase Functions**在完成逻辑判断后，命令FCM去发送通知。
    9.  **通知下发:** FCM将通知推送到用户的设备上。
    10. **业务API调用:** 对于需要后台逻辑处理的特定任务（如购买验证、发现页聚合），App会调用我们自己编写的**Firebase Functions** HTTP端点。
    11. **购买验证:** **Firebase Functions**作为受信任的服务器，向Google Play的后台API验证购买票据的有效性。

---
这份详尽的组件图，为我们接下来的所有技术讨论提供了一个统一的、可视化的宏观语境。