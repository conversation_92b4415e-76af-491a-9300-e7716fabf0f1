
我将为您输出**产出物 6: 《可观测性设计草案》**的最终、完整版本。这份文档是我们对系统运维和线上质量保障策略的全面总结，是我们从“构建”阶段迈向“可靠运营”阶段的战略蓝图。

---

### **产出物 6: 《可观测性设计草案 (Observability Design Draft)》**

**版本: 1.0 (最终版)**
**日期: 2025-08-22**
**目标: 确保IMList系统的健康、稳定与可维护性，实现主动发现问题、快速定位问题和数据驱动优化的能力。**

---

#### **1. 核心原则**

*   **1.1 主动发现 (Proactive Detection):** 我们的目标是，在用户感知到问题之前，通过主动监控和告警发现系统的异常行为。运维团队不应依赖用户反馈来获知系统故障。
*   **1.2 快速定位 (Rapid Root Cause Analysis):** 当问题发生时，我们必须能够通过关联的日志和追踪，在分钟级别内定位到问题的根本原因，而不是在孤立的日志海洋中捞针。
*   **1.3 数据驱动 (Data-Driven Optimization):** 所有的性能优化和稳定性改进，都必须基于从线上真实用户环境中采集的量化指标，而非主观臆测。

---

#### **2. 日志策略 (Logging)**

*   **2.1 日志格式与结构:**
    *   **强制规范:** 所有由Firebase Functions产生的日志，**必须**使用**结构化的JSON格式**。
    *   **基础字段:** 每一条日志记录都应包含一个标准的上下文对象，至少包括：
        *   `severity`: 日志级别 (见2.2)。
        *   `message`: 人类可读的日志信息。
        *   `timestamp`: 事件发生的时间戳。
        *   `function_name`: 产生日志的云函数名称。
        *   `trace_id`: 关联请求的分布式追踪ID (见4.1)。
    *   **业务字段:** 关键业务日志应包含额外的业务上下文，例如：`userId`, `tmdbId`, `purchaseId`。

*   **2.2 日志级别:**
    *   **DEBUG:** 用于本地开发阶段的详细调试信息，例如打印完整的变量对象。**严禁**在线上生产环境启用。
    *   **INFO:** 用于记录关键业务流程的入口、出口和成功执行点。例如: `{"severity": "INFO", "message": "Purchase verification successful for user X", "userId": "XYZ", "trace_id": "abc..."}`。
    *   **WARN:** 用于记录可预期的、非致命的异常情况，应用可以从中恢复。例如: `{"severity": "WARN", "message": "TMDB API responded slowly: 2500ms", "trace_id": "abc..."}`。
    *   **ERROR:** 用于记录所有未预期的、导致功能失败或流程中断的错误。**必须**包含完整的错误堆栈信息 (`stack_trace`) 和相关的请求参数，以便复现问题。

*   **2.3 集中收集与查询:**
    *   所有日志将自动被Firebase Functions运行时收集到 **Google Cloud Logging**，作为日志的“单一事实来源”。
    *   团队应接受培训，熟练使用Cloud Logging的查询语言，根据`severity`, `trace_id`, `userId`等字段进行高效的过滤和分析。

---

#### **3. 监控与告警策略 (Monitoring & Alerting)**

*   **3.1 监控工具栈:**
    *   **后端:** 主要使用 **Firebase / Google Cloud Monitoring** 套件。
    *   **客户端:** 主要使用 **Firebase Crashlytics** (用于崩溃和非致命异常) 和 **Firebase Performance Monitoring** (用于性能指标)。

*   **3.2 核心监控指标:**
    *   **3.2.1 业务指标 (Business Metrics):**
        *   **用户增长:** 用户注册数 / 日活 (来源: Firebase Analytics)。
        *   **付费转化:** 付费用户数 / 付费转化率 (来源: 基于`users.isPremium`字段创建的自定义Cloud Monitoring指标)。
        *   **API健康度:** 核心API（`/discovery`, `/shows/{id}`）的调用成功率 (来源: Cloud Functions Dashboard)。
    *   **3.2.2 系统指标 (System Metrics):**
        *   **云函数健康度:**
            *   函数执行失败率 (尤其是`purchases/verify`和`user/me`)。
            *   函数执行时长 P95/P99 (监控API延迟)。
        *   **数据库健康度:**
            *   Firestore 读/写/删除操作数 (监控负载和成本)。
            *   Firestore 安全规则拒绝次数 (监控潜在的攻击或客户端bug)。
        *   **客户端健康度:**
            *   无崩溃用户率 (目标 > 99.8%)。
            *   ANR率 (应用无响应率)。

*   **3.3 告警规则与渠道:**
    *   告警将在 **Google Cloud Alerting** 中配置。
    *   **P0级告警 (紧急 - 触发短信/电话/PagerDuty):**
        *   `POST /purchases/verify` 函数失败率在5分钟内 > 5% (直接影响核心收入)。
        *   客户端无崩溃用户率在1小时内跌破 99%。
        *   `DELETE /user/me` 函数失败率 > 1% (1小时内) (存在合规风险)。
    *   **P1级告警 (重要 - 触发邮件/Slack频道):**
        *   任何核心API的P99延迟在10分钟内持续高于2秒。
        *   `POST /tasks/check-episode-updates` 定时任务连续执行失败3次。
        *   Firestore的总体安全规则拒绝次数出现异常尖峰增长。

---

#### **4. 分布式追踪策略 (Distributed Tracing)**

*   **4.1 追踪ID (Trace ID) 的生成与传递:**
    *   **客户端责任:** IMList Android App在发起每一次对我们后台API的HTTP请求时，**必须**在请求头中生成并附带一个唯一的追踪ID。格式为UUIDv4，HTTP头为 `X-Request-ID`。
*   **4.2 日志与追踪的关联:**
    *   **后台责任:** 所有处理该请求的Firebase Functions，在其输出的**每一条结构化JSON日志**中，都**必须**包含从请求头中获取的`trace_id`字段。
*   **4.3 追踪分析工具:**
    *   我们将利用 **Google Cloud Trace**。它能自动分析和可视化带有相同`trace_id`的日志和函数执行时间，生成请求的端到端火焰图，帮助我们快速定位性能瓶颈。
*   **4.4 最终目标:**
    *   对于任何线上报告的问题，我们的标准排查流程（SOP）的第一步应该是：“**请提供这次操作的Trace ID**”。通过这一个ID，我们就应该能在Google Cloud Logging中，筛选出一次用户操作所触发的、跨越所有后台组件的完整日志链路。

---
这份详尽的可观测性设计草案，为我们构建一个透明、健壮、可维护的线上系统提供了完整的行动指南。