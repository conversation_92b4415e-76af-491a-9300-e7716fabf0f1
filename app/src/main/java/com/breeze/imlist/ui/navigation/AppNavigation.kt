package com.breeze.imlist.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController

object Routes {
    const val LOGIN_SCREEN = "login"
    const val REGISTRATION_SCREEN = "registration"
    const val HOME_SCREEN = "home"
}

@Composable
fun AppNavigation() {
    val navController = rememberNavController()

    NavHost(navController = navController, startDestination = Routes.REGISTRATION_SCREEN) {
        composable(Routes.REGISTRATION_SCREEN) {
            // RegistrationScreen will go here
        }
        composable(Routes.LOGIN_SCREEN) {
            // LoginScreen will go here
        }
        composable(Routes.HOME_SCREEN) {
            // HomeScreen will go here
        }
    }
}